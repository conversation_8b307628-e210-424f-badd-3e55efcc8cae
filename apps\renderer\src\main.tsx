import React, { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'

// Error boundary for the entire app
class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean; error?: Error }> {
	constructor(props: { children: React.ReactNode }) {
		super(props)
		this.state = { hasError: false }
	}

	static getDerivedStateFromError(error: Error) {
		return { hasError: true, error }
	}

	componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
		console.error('React Error Boundary caught an error:', error, errorInfo)
	}

	render() {
		if (this.state.hasError) {
			return (
				<div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
					<div className="text-center">
						<h1 className="text-2xl font-bold text-red-400 mb-4">Something went wrong</h1>
						<p className="text-gray-300 mb-4">{this.state.error?.message || 'An unexpected error occurred'}</p>
						<button
							onClick={() => window.location.reload()}
							className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
						>
							Reload App
						</button>
					</div>
				</div>
			)
		}

		return this.props.children
	}
}

// Wait for DOM to be ready and ElectronAPI to be available
const initializeApp = () => {
	const rootElement = document.getElementById('root')
	if (!rootElement) {
		console.error('Root element not found')
		return
	}

	// Check if ElectronAPI is available
	if (typeof window !== 'undefined' && !(window as any).electronAPI) {
		console.warn('ElectronAPI not available, waiting...')
		setTimeout(initializeApp, 100)
		return
	}

	try {
		const root = createRoot(rootElement)
		root.render(
			<StrictMode>
				<ErrorBoundary>
					<App />
				</ErrorBoundary>
			</StrictMode>
		)
	} catch (error) {
		console.error('Failed to initialize React app:', error)
		// Fallback rendering
		rootElement.innerHTML = `
      <div style="min-height: 100vh; background: #111827; color: white; display: flex; align-items: center; justify-content: center;">
        <div style="text-align: center;">
          <h1 style="color: #ef4444; margin-bottom: 1rem;">Failed to load application</h1>
          <p style="color: #9ca3af; margin-bottom: 1rem;">Error: ${error}</p>
          <button onclick="window.location.reload()" style="padding: 0.5rem 1rem; background: #2563eb; color: white; border: none; border-radius: 0.5rem; cursor: pointer;">
            Reload
          </button>
        </div>
      </div>
    `
	}
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
	document.addEventListener('DOMContentLoaded', initializeApp)
} else {
	initializeApp()
}
