import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'

// Error handling wrapper for IPC calls
const safeInvoke = async (channel: string, ...args: any[]) => {
	try {
		return await ipcRenderer.invoke(channel, ...args)
	} catch (error) {
		console.error(`IPC invoke error for ${channel}:`, error)
		return { success: false, message: `IPC error: ${error}` }
	}
}

// Define the API that will be exposed to the renderer process
const electronAPI = {
	// Settings management
	getSettings: () => safeInvoke('get-settings'),
	saveSettings: (settings: any) => safeInvoke('save-settings', settings),

	// Dialog utilities
	showErrorDialog: (title: string, content: string) => safeInvoke('show-error-dialog', title, content),
	showInfoDialog: (title: string, content: string) => safeInvoke('show-info-dialog', title, content),

	// Bot control
	startBot: (settings: any) => safeInvoke('start-bot', settings),
	stopBot: () => safeInvoke('stop-bot'),
	getBotStatus: () => safeInvoke('get-bot-status'),
	updateBotSettings: (settings: any) => safeInvoke('update-bot-settings', settings),

	// Browser control
	initializeBrowser: () => safeInvoke('initialize-browser'),
	checkLoginStatus: () => safeInvoke('check-login-status'),
	closeBrowser: () => safeInvoke('close-browser'),
	getBrowserStatus: () => safeInvoke('get-browser-status'),

	// Bot status updates (for real-time data)
	onBotStatusUpdate: (callback: (status: any) => void) => {
		ipcRenderer.on('bot-status-update', (_, status) => callback(status))
	},

	onPriceUpdate: (callback: (price: number) => void) => {
		ipcRenderer.on('price-update', (_, price) => callback(price))
	},

	onTradeResult: (callback: (result: any) => void) => {
		ipcRenderer.on('trade-result', (_, result) => callback(result))
	},

	onStopLossTriggered: (callback: (data: any) => void) => {
		ipcRenderer.on('stop-loss-triggered', (_, data) => callback(data))
	},

	onBotStatusMessage: (callback: (message: string) => void) => {
		ipcRenderer.on('bot-status-message', (_, message) => callback(message))
	},

	// Remove listeners
	removeAllListeners: (channel: string) => {
		ipcRenderer.removeAllListeners(channel)
	},

	// TODO: Remove this on production
	// Debug and utility functions
	debugPriceElements: () => safeInvoke('debug-price-elements'),
	selectTradingAsset: () => safeInvoke('select-trading-asset')
}

// Expose the API to the renderer process with error handling
try {
	contextBridge.exposeInMainWorld('electronAPI', electronAPI)
	console.log('ElectronAPI successfully exposed to renderer process')
} catch (error) {
	console.error('Failed to expose ElectronAPI:', error)
	// Fallback: expose directly to global object (less secure but functional)
	;(global as any).electronAPI = electronAPI
}

// Type definitions for TypeScript (will be used in renderer)
export type ElectronAPI = typeof electronAPI
